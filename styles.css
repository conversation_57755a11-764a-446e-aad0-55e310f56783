/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Header Styles */
.main-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 20px 50px;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    height: 2vw;
}

.header-logo {
    height: 40px;
    width: auto;
}

.logo span {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
}

.connect-btn {
    background: #4ade80;
    color: #000000;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.connect-btn:hover {
    background: #22c55e;
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #0a0a0a 70%);
    position: relative;
    padding: 100px 20px 50px;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('statics/bg.png');
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    z-index: -1;
}

.hero-content {
    max-width: 800px;
    z-index: 2;
}

.hero-logo {
    margin-bottom: 30px;
}

.main-logo {
    width: 380px;
    height: 380px;
    object-fit: contain;
    border-radius: 0;
    border: none;
    background: transparent;
    padding: 0;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #4ade80, #22c55e, #16a34a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #d9d9df;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    max-width: 500px;
    margin: 0 auto;
}

.wallet-input {
    flex: 1;
    padding: 16px 20px;
    border: 2px solid rgba(74, 222, 128, 0.3);
    border-radius: 8px;
    background: rgba(26, 26, 46, 0.3);
    color: #a1a1aa;
    font-size: 16px;
    font-family: 'Inter', sans-serif;
    outline: none;
    transition: all 0.3s ease;
    min-width: 250px;
}

.wallet-input:focus {
    border-color: rgba(74, 222, 128, 0.6);
    background: rgba(26, 26, 46, 0.5);
}

.btn-connect {
    padding: 16px 32px;
    border: none;
    border-radius: 8px;
    background: #4ade80;
    color: #000000;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    white-space: nowrap;
}

.btn-connect:hover {
    background: #22c55e;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(74, 222, 128, 0.3);
}

.hero-links {
    margin-top: 30px;
}

.whitepaper-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #4ade80;
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    padding: 12px 20px;
    border: 1px solid rgba(74, 222, 128, 0.3);
    border-radius: 8px;
    background: rgba(26, 26, 46, 0.3);
    transition: all 0.3s ease;
}

.whitepaper-link:hover {
    border-color: rgba(74, 222, 128, 0.6);
    background: rgba(26, 26, 46, 0.5);
    transform: translateY(-2px);
}

/* About Section */
.about-section {
    padding: 100px 20px;
    background: #0f0f0f;
}

.section-content {
    max-width: 1200px;
    margin: 0 auto;
}

.about-section h2 {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 60px;
    color: #ffffff;
}

.about-content {
    display: block;
    text-align: left;
    margin-bottom: 40px;
}

.about-text p {
    font-size: 1.1rem;
    color: #e8e8f0;
    line-height: 1.8;
    margin-bottom: 20px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    padding-left: 20px;
}

.about-text p::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.7em;
    width: 8px;
    height: 8px;
    background-color: #00ff88;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.5);
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
}

.about-image img {
    width: 80%;
    max-width: 600px;
    height: auto;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.about-image img:hover {
    transform: scale(1.05);
}

/* Features Section */
.features-section {
    padding: 100px 20px;
    background: #0a0a0a;
}

.features-section h2 {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 60px;
    color: #ffffff;
}

.features-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.features-left {
    display: flex;
    flex-direction: column;
    gap: 30px;
    grid-row: 1 / 3;
}

.features-right {
    display: flex;
    flex-direction: column;
    gap: 30px;
    grid-row: 1 / 3;
}

.feature-block {
    background: rgba(26, 26, 46, 0.3);
    border: 1px solid rgba(74, 222, 128, 0.2);
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
}

.feature-block:hover {
    border-color: rgba(74, 222, 128, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(74, 222, 128, 0.1);
}

/* Feature header with icon and title */
.feature-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.feature-icon {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    object-fit: contain;
    transition: all 0.3s ease;
}

.feature-block:hover .feature-icon {
    transform: scale(1.1);
}

.feature-block h3 {
    font-size: 1.3rem;
    margin: 0;
    color: #4ade80;
    font-weight: 600;
}

.feature-block p {
    color: #a1a1aa;
    line-height: 1.6;
    font-size: 0.95rem;
}

.feature-images-top {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 30px;
}

.feature-images-top img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.feature-images-top img:hover {
    transform: scale(1.05);
}

.features-bottom-images {
    grid-column: 1 / 2;
    grid-row: 3;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
}

.features-bottom-images img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
    transition: transform 0.3s ease;
}

.features-bottom-images img:hover {
    transform: scale(1.02);
}

/* Footer */
footer {
    background: #000000;
    padding: 40px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-logo p {
    color: #a1a1aa;
    font-size: 14px;
}

.contact-email {
    margin-top: 10px;
}

.contact-email a {
    color: #4ade80;
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-email a:hover {
    color: #22c55e;
}

.social-links {
    display: flex;
    gap: 20px;
}

.social-links a {
    opacity: 0.7;
    transition: opacity 0.3s ease;
    text-decoration: none;
}

.social-links a:hover {
    opacity: 1;
}

.social-links img {
    width: 24px;
    height: 24px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-header {
        padding: 15px 20px;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        max-width: 100%;
    }
    
    .wallet-input {
        width: 100%;
        max-width: 300px;
        min-width: unset;
    }
    
    .btn-connect {
        width: 100%;
        max-width: 300px;
    }
    
    .features-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto;
        gap: 30px;
    }

    .features-left {
        grid-row: auto;
    }

    .features-right {
        grid-row: auto;
    }

    .features-bottom-images {
        grid-column: 1;
        grid-row: auto;
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .feature-images-top {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .about-section h2,
    .features-section h2 {
        font-size: 2rem;
    }
    
    .main-logo {
        width: 250px;
        height: 250px;
    }
}
